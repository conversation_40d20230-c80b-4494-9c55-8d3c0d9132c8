<?php
//ini_set('display_errors','on');
ini_set('display_errors','off');
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/admin/Control.php");
require_once(LIBRARY_DIR."library/classes/admin/Account.php");
require_once("common.php");
Authenticator::isAccountLogin("ss_account");

$oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
if (!$oDB->open()) {
  die(CONNECTION_DB_FAILED.$oDB->error());
}

$action = trim($_GET['action']);
$filename = trim($_GET['filename']); // 使用檔案名稱
$type = trim($_GET['type']) ?: 'current';
$userId = $_SESSION['ss_account']['account'] ?? 'admin';

if ($action == 'single' && !empty($filename)) {
    // 單一檔案下載
    downloadSingleFile($oDB, $filename, $type, $userId);
} elseif ($action == 'all') {
    // 全部下載
    downloadAllFiles($oDB, $type, $userId);
} else {
    // 錯誤處理
    header('HTTP/1.1 400 Bad Request');
    echo 'Invalid request';
    exit;
}

/**
 * 下載單一XML檔案
 */
function downloadSingleFile($oDB, $filename, $type, $userId) {
    // 從資料庫取得檔案資訊
    $sql = "SELECT * FROM convert_files WHERE filename = :filename";
    $rows = $oDB->execute($sql, [':filename' => $filename]);

    if (!$rows || !$oDB->fetchRow()) {
        header('HTTP/1.1 404 Not Found');
        echo 'File record not found';
        exit;
    }

    $fileInfo = $oDB->rowArray;
    $filePath = $fileInfo['file_path'];

    if (!file_exists($filePath)) {
        header('HTTP/1.1 404 Not Found');
        echo 'File not found on server';
        exit;
    }

    // 下載記錄功能已移除

    // 設定下載標頭
    header('Content-Type: application/xml; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . filesize($filePath));
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');

    // 輸出檔案內容
    readfile($filePath);
    exit;
}

/**
 * 下載全部XML檔案（ZIP格式）
 */
function downloadAllFiles($oDB, $type, $userId) {
    // 根據類型取得對應的檔案列表
    if ($type === 'current') {
        // 取得最新批次的檔案
        $convertResult = getConvertList($oDB, 'current', '', 1, 0);
    } else {
        // 取得近一年的所有檔案
        $convertResult = getConvertList($oDB, 'history', '', 1, 0);
    }

    $xmlFiles = [];
    foreach ($convertResult['data'] as $fileInfo) {
        if (file_exists($fileInfo['filepath'])) {
            $xmlFiles[] = $fileInfo['filepath'];
        }
    }

    if (empty($xmlFiles)) {
        header('HTTP/1.1 404 Not Found');
        echo 'No XML files found';
        exit;
    }
    
    // 建立臨時目錄
    $tempDir = sys_get_temp_dir() . '/convert_' . uniqid();
    if (!mkdir($tempDir, 0777, true)) {
        header('HTTP/1.1 500 Internal Server Error');
        echo 'Cannot create temporary directory';
        exit;
    }
    
    // 建立ZIP檔案
    $zipFilename = "all_{$type}_xml_" . date('YmdHis') . '.zip';
    $zipPath = $tempDir . '/' . $zipFilename;

    $zip = new ZipArchive();
    if ($zip->open($zipPath, ZipArchive::CREATE) !== TRUE) {
        header('HTTP/1.1 500 Internal Server Error');
        echo 'Cannot create zip file';
        exit;
    }

    // 將XML檔案加入ZIP
    foreach ($xmlFiles as $xmlFile) {
        $zip->addFile($xmlFile, basename($xmlFile));
    }
    $zip->close();

    // 下載記錄功能已移除

    // 設定下載標頭
    header('Content-Type: application/zip');
    header('Content-Disposition: attachment; filename="' . $zipFilename . '"');
    header('Content-Length: ' . filesize($zipPath));
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');

    // 輸出ZIP檔案
    readfile($zipPath);

    // 清理臨時檔案
    array_map('unlink', glob($tempDir . '/*'));
    rmdir($tempDir);

    exit;
}


?>
