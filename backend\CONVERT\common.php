<?php
// CSV轉XML工具共用函數

/**
 * 取得轉檔檔案列表
 * @param DB $oDB 資料庫連接物件
 * @param string $type 類型 (current/history)
 * @param string $keyword 搜尋關鍵字
 * @param int $page 頁數
 * @param int $pageSize 每頁筆數
 * @param string $dateFrom 開始日期
 * @param string $dateTo 結束日期
 * @return array
 */
function getConvertList($oDB, $type = 'current', $keyword = '', $page = 1, $pageSize = 20, $dateFrom = '', $dateTo = '') {
    $files = [];

    if ($type === 'current') {
        // Current: 只顯示最新一次轉檔的結果
        $files = getLatestConvertBatch($oDB, $keyword, $dateFrom, $dateTo);
    } else {
        // History: 顯示近一年的所有資料
        $files = getHistoryConvertData($oDB, $keyword, $dateFrom, $dateTo);
    }

    $total = count($files);

    // 分頁處理
    if ($pageSize > 0) {
        $offset = ($page - 1) * $pageSize;
        $files = array_slice($files, $offset, $pageSize);
    }

    return [
        'data' => $files,
        'total' => $total
    ];
}

/**
 * 取得最新一次轉檔批次的檔案
 * @param DB $oDB 資料庫連接物件
 * @param string $keyword 搜尋關鍵字
 * @param string $dateFrom 開始日期
 * @param string $dateTo 結束日期
 * @return array
 */
function getLatestConvertBatch($oDB, $keyword = '', $dateFrom = '', $dateTo = '') {
    try {
        // 取得最新的批次
        $sql = "SELECT batch_id FROM convert_batch ORDER BY created_at DESC LIMIT 1";
        $rows = $oDB->execute($sql, []);

        if (!$rows || !$oDB->fetchRow()) {
            return [];
        }

        $latestBatchId = $oDB->rowArray['batch_id'];

    // 取得該批次的所有檔案
    $sql = "SELECT cf.*, cb.original_filename, cb.created_at as batch_created_at
            FROM convert_files cf
            JOIN convert_batch cb ON cf.batch_id = cb.batch_id
            WHERE cf.batch_id = :batchId";

    $params = [':batchId' => $latestBatchId];

    // 如果有搜尋關鍵字，加入搜尋條件
    if (!empty($keyword)) {
        $sql .= " AND (cf.filename LIKE :keyword OR cf.cylinder_no LIKE :keyword OR cf.seq LIKE :keyword)";
        $params[':keyword'] = "%{$keyword}%";
    }

    // 如果有日期範圍，加入日期搜尋條件
    if (!empty($dateFrom)) {
        $sql .= " AND DATE(cb.created_at) >= :dateFrom";
        $params[':dateFrom'] = $dateFrom;
    }
    if (!empty($dateTo)) {
        $sql .= " AND DATE(cb.created_at) <= :dateTo";
        $params[':dateTo'] = $dateTo;
    }

    $sql .= " ORDER BY cf.seq ASC";

    $files = [];
    $rows = $oDB->execute($sql, $params);
    if ($rows) {
        while ($oDB->fetchRow()) {
            $files[] = formatFileInfo($oDB->rowArray);
        }
    }

    return $files;

    } catch (Exception $e) {
        // 如果發生錯誤，返回空陣列
        return [];
    }
}

/**
 * 取得近一年的歷史轉檔資料
 * @param DB $oDB 資料庫連接物件
 * @param string $keyword 搜尋關鍵字
 * @param string $dateFrom 開始日期
 * @param string $dateTo 結束日期
 * @return array
 */
function getHistoryConvertData($oDB, $keyword = '', $dateFrom = '', $dateTo = '') {
    try {
        // 取得近一年的所有檔案
        $oneYearAgo = date('Y-m-d H:i:s', strtotime('-1 year'));

        $sql = "SELECT cf.*, cb.original_filename, cb.created_at as batch_created_at
                FROM convert_files cf
                JOIN convert_batch cb ON cf.batch_id = cb.batch_id
                WHERE cb.created_at >= :oneYearAgo";

        $params = [':oneYearAgo' => $oneYearAgo];

        // 如果有搜尋關鍵字，加入搜尋條件
        if (!empty($keyword)) {
            $sql .= " AND (cf.filename LIKE :keyword OR cf.cylinder_no LIKE :keyword OR cf.seq LIKE :keyword)";
            $params[':keyword'] = "%{$keyword}%";
        }

        // 如果有日期範圍，覆蓋預設的一年限制
        if (!empty($dateFrom) || !empty($dateTo)) {
            // 移除原本的一年限制
            $sql = str_replace("WHERE cb.created_at >= :oneYearAgo", "WHERE 1=1", $sql);
            unset($params[':oneYearAgo']);

            if (!empty($dateFrom)) {
                $sql .= " AND DATE(cb.created_at) >= :dateFrom";
                $params[':dateFrom'] = $dateFrom;
            }
            if (!empty($dateTo)) {
                $sql .= " AND DATE(cb.created_at) <= :dateTo";
                $params[':dateTo'] = $dateTo;
            }
        }

        $sql .= " ORDER BY cb.created_at DESC, cf.seq ASC";

        $files = [];
        $rows = $oDB->execute($sql, $params);
        if ($rows) {
            while ($oDB->fetchRow()) {
                $files[] = formatFileInfo($oDB->rowArray);
            }
        }

        return $files;

    } catch (Exception $e) {
        // 如果發生錯誤，返回空陣列
        return [];
    }
}

/**
 * 格式化檔案資訊陣列
 * @param array $dbRow 資料庫查詢結果
 * @return array
 */
function formatFileInfo($dbRow) {
    return [
        'id' => $dbRow['id'],
        'filename' => $dbRow['filename'],
        'filepath' => $dbRow['file_path'],
        'creation_date' => date('Y/m/d H:i:s', strtotime($dbRow['batch_created_at'])),
        'file_size' => $dbRow['file_size'],
        'cylinder_no' => $dbRow['cylinder_no'] ?: '',
        'seq' => $dbRow['seq'],
        'batch_id' => $dbRow['batch_id'],
        'xml_creation_date' => $dbRow['xml_creation_date'] ? date('Y/m/d H:i:s', strtotime($dbRow['xml_creation_date'])) : '',
        'file_status' => $dbRow['file_status']
    ];
}

/**
 * 取得Current和History的總筆數
 * @param DB $oDB 資料庫連接物件
 * @param string $keyword 搜尋關鍵字
 * @param string $dateFrom 開始日期
 * @param string $dateTo 結束日期
 * @return array ['current' => int, 'history' => int]
 */
function getConvertCounts($oDB, $keyword = '', $dateFrom = '', $dateTo = '') {
    try {
        $counts = ['current' => 0, 'history' => 0];

        // 取得最新批次ID
        $latestBatchSql = "SELECT batch_id FROM convert_batch ORDER BY created_at DESC LIMIT 1";
        $latestBatchRows = $oDB->execute($latestBatchSql, []);

        $latestBatchId = null;
        if ($latestBatchRows && $oDB->fetchRow()) {
            $latestBatchId = $oDB->rowArray['batch_id'];
        }

        // 建立搜尋條件
        $searchCondition = '';
        $searchParams = [];
        if (!empty($keyword)) {
            $searchCondition .= " AND (cf.filename LIKE :keyword OR cf.cylinder_no LIKE :keyword OR cf.seq LIKE :keyword)";
            $searchParams[':keyword'] = "%{$keyword}%";
        }

        // 建立日期搜尋條件
        $dateCondition = '';
        if (!empty($dateFrom)) {
            $dateCondition .= " AND DATE(cb.created_at) >= :dateFrom";
            $searchParams[':dateFrom'] = $dateFrom;
        }
        if (!empty($dateTo)) {
            $dateCondition .= " AND DATE(cb.created_at) <= :dateTo";
            $searchParams[':dateTo'] = $dateTo;
        }

        // 計算Current筆數（最新批次）
        if ($latestBatchId) {
            $currentSql = "SELECT COUNT(*) as count FROM convert_files cf
                        JOIN convert_batch cb ON cf.batch_id = cb.batch_id
                        WHERE cf.batch_id = :batchId" . $searchCondition . $dateCondition;
            $currentParams = array_merge([':batchId' => $latestBatchId], $searchParams);
            $currentRows = $oDB->execute($currentSql, $currentParams);
            if ($currentRows && $oDB->fetchRow()) {
                $counts['current'] = (int)$oDB->rowArray['count'];
            }
        }

        // 計算History筆數
        $historySql = "SELECT COUNT(*) as count FROM convert_files cf
                       JOIN convert_batch cb ON cf.batch_id = cb.batch_id
                       WHERE 1=1" . $searchCondition . $dateCondition;

        // 如果沒有指定日期範圍，則使用預設的一年限制
        if (empty($dateFrom) && empty($dateTo)) {
            $oneYearAgo = date('Y-m-d H:i:s', strtotime('-1 year'));
            $historySql .= " AND cb.created_at >= :oneYearAgo";
            $searchParams[':oneYearAgo'] = $oneYearAgo;
        }

        $historyRows = $oDB->execute($historySql, $searchParams);
        if ($historyRows && $oDB->fetchRow()) {
            $counts['history'] = (int)$oDB->rowArray['count'];
        }

        return $counts;

    } catch (Exception $e) {
        return ['current' => 0, 'history' => 0];
    }
}

/**
 * 清理超過一年的檔案
 * @return array 清理結果
 */
function cleanOldFiles() {
    $result = ['success' => false, 'message' => '', 'deleted_count' => 0];

    try {
        $baseDir = $_SERVER['DOCUMENT_ROOT'] . '/resonac-all/uploads/convert/';
        $currentDir = $baseDir . 'current/';
        $historyDir = $baseDir . 'history/';

        $oneYearAgo = time() - (365 * 24 * 60 * 60); // 一年前的時間戳
        $deleteCount = 0;

        // 檢查current目錄
        if (is_dir($currentDir)) {
            $files = glob($currentDir . '*.xml');
            foreach ($files as $file) {
                if (filemtime($file) < $oneYearAgo) {
                    if (unlink($file)) {
                        $deleteCount++;
                    }
                }
            }
        }

        // 檢查history目錄
        if (is_dir($historyDir)) {
            $files = glob($historyDir . '*.xml');
            foreach ($files as $file) {
                if (filemtime($file) < $oneYearAgo) {
                    if (unlink($file)) {
                        $deleteCount++;
                    }
                }
            }
        }

        $result['success'] = true;
        $result['deleted_count'] = $deleteCount;
        $result['message'] = $deleteCount > 0 ? "成功清理 {$deleteCount} 個超過一年的檔案" : "沒有需要清理的檔案";

    } catch (Exception $e) {
        $result['message'] = "清理檔案時發生異常：" . $e->getMessage();
    }

    return $result;
}

/**
 * CSV轉XML處理
 * @param DB $oDB 資料庫連接物件
 * @param array $file 上傳的檔案資訊
 * @param string $userId 使用者ID
 * @return array 結果訊息
 */
function convertCSVToXML($oDB, $file, $userId = 'admin') {
    $result = ['success' => false, 'message' => '', 'xml_files' => []];

    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        $result['message'] = '請選擇要轉換的檔案';
        return $result;
    }

    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if ($fileExtension !== 'csv') {
        $result['message'] = '只支援CSV格式檔案';
        return $result;
    }

    // 設定輸出目錄
    $outputDir = $_SERVER['DOCUMENT_ROOT'] . '/resonac-all/uploads/convert/';
    if (!is_dir($outputDir)) {
        mkdir($outputDir, 0777, true);
    }

    // 生成批次ID
    $batchId = date('YmdHis') . '_' . uniqid();
    $result['batch_id'] = $batchId;

    // 在資料庫中建立批次記錄
    $batchSql = "INSERT INTO convert_batch (batch_id, original_filename, batch_status, created_by) VALUES (:batchId, :filename, 'processing', :userId)";
    $batchParams = [
        ':batchId' => $batchId,
        ':filename' => $file['name'],
        ':userId' => $userId
    ];
    $batchRows = $oDB->execute($batchSql, $batchParams);

    if (!$batchRows) {
        $result['message'] = '無法建立批次記錄 - 資料庫錯誤: ' . $oDB->error();
        return $result;
    }

    try {
        // 讀取CSV檔案
        if (($handle = fopen($file['tmp_name'], "r")) !== FALSE) {
            $rowCount = 0;
            $successCount = 0;
            $failedCount = 0;
            $errors = [];
            $headers = [];

            while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                $rowCount++;

                // 第一行作為XML標籤名稱
                if ($rowCount == 1) {
                    $headers = array_map('trim', $data);
                    continue;
                }

                // 處理每一行資料，轉換為XML
                if (count($data) > 0) {
                    // 從CSV資料中提取必要資訊
                    $materialNumber = '';
                    $lotNumber = '';
                    $cylinderNumber = '';

                    // 根據標題找到對應的欄位值
                    for ($i = 0; $i < count($headers); $i++) {
                        $header = strtolower(trim($headers[$i]));
                        $value = isset($data[$i]) ? trim($data[$i]) : '';

                        if (strpos($header, 'materialnumber') !== false || strpos($header, 'material_number') !== false) {
                            $materialNumber = $value;
                        } elseif (strpos($header, 'lotnumber') !== false || strpos($header, 'lot_number') !== false) {
                            $lotNumber = $value;
                        } elseif (strpos($header, 'cylinder number') !== false || strpos($header, 'cylindernumber') !== false || strpos($header, 'cylinder_number') !== false) {
                            $cylinderNumber = $value;
                        }
                    }

                    // 根據MaterialNumber找到對應的檢驗項目設定
                    $inspectionSettings = [];
                    if (!empty($materialNumber)) {
                        $settingsSql = "SELECT cis.item_name, cis.specification, cis.detection_limit, cis.ucl
                                        FROM convert_inspection_settings cis
                                        JOIN convert_inspection_types cit ON cis.type_id = cit.id
                                        WHERE cit.type_name = :materialNumber AND cis.is_active = 1
                                        ORDER BY cis.sort_order, cis.id";
                        $settingsResult = $oDB->execute($settingsSql, [':materialNumber' => $materialNumber]);

                        if ($settingsResult) {
                            while ($oDB->fetchRow()) {
                                $inspectionSettings[] = [
                                    'itemName' => $oDB->rowArray['item_name'],
                                    'specification' => $oDB->rowArray['specification'],
                                    'detectionLimit' => floatval($oDB->rowArray['detection_limit']),
                                    'ucl' => floatval($oDB->rowArray['ucl'])
                                ];
                            }
                        }
                    }

                    // 檢驗項目驗證
                    if (!empty($inspectionSettings)) {
                        $validationErrors = validateInspectionItems($headers, $data, $inspectionSettings);
                        if (!empty($validationErrors)) {
                            $errors = array_merge($errors, $validationErrors);
                            $failedCount += 3; // 3個檔案都失敗
                            continue; // 跳過這一行的處理
                        }
                    }

                    // 生成3個檔案（SG08, SG18, SG28）
                    $prefixes = ['SG08', 'SG18', 'SG28'];

                    foreach ($prefixes as $prefix) {
                        // 生成XML檔案名稱：{prefix}_1039151_{MaterialNumber}_{LotNumber}_{CylinderNumber}
                        $xmlFilename = "{$prefix}_1039151_{$materialNumber}_{$lotNumber}_{$cylinderNumber}.xml";
                        $xmlPath = $outputDir . $xmlFilename;

                        // 生成XML內容
                        $xmlContent = generateXMLFromCSVRow($headers, $data, $userId, $batchId, $prefix, $inspectionSettings);

                        if ($xmlContent) {
                            // 寫入XML檔案
                            if (file_put_contents($xmlPath, $xmlContent)) {
                                $xmlCreationDate = null;

                                // 嘗試解析日期
                                for ($i = 0; $i < count($headers); $i++) {
                                    $header = strtolower(trim($headers[$i]));
                                    if (strpos($header, 'date') !== false || strpos($header, 'time') !== false) {
                                        $dateValue = isset($data[$i]) ? trim($data[$i]) : '';
                                        if (!empty($dateValue)) {
                                            $xmlCreationDate = date('Y-m-d H:i:s', strtotime($dateValue));
                                            break;
                                        }
                                    }
                                }

                                // 在資料庫中記錄檔案資訊
                                $fileSql = "INSERT INTO convert_files (batch_id, filename, file_path, file_size, cylinder_no, seq, xml_creation_date, file_status) VALUES (:batchId, :filename, :filePath, :fileSize, :cylinderNo, :seq, :xmlCreationDate, 'created')";
                                $fileParams = [
                                    ':batchId' => $batchId,
                                    ':filename' => $xmlFilename,
                                    ':filePath' => $xmlPath,
                                    ':fileSize' => strlen($xmlContent),
                                    ':cylinderNo' => $cylinderNumber,
                                    ':seq' => $prefix,
                                    ':xmlCreationDate' => $xmlCreationDate
                                ];

                                if ($oDB->execute($fileSql, $fileParams)) {
                                    $successCount++;
                                    $result['xml_files'][] = [
                                        'filename' => $xmlFilename,
                                        'path' => $xmlPath,
                                        'seq' => $prefix
                                    ];
                                } else {
                                    $failedCount++;
                                    $errors[] = "第 {$rowCount} 行 ({$prefix})：無法記錄檔案資訊到資料庫";
                                    // 刪除已建立的XML檔案
                                    if (file_exists($xmlPath)) {
                                        unlink($xmlPath);
                                    }
                                }
                            } else {
                                $failedCount++;
                                $errors[] = "第 {$rowCount} 行 ({$prefix})：無法寫入XML檔案 {$xmlFilename}";
                            }
                        } else {
                            $failedCount++;
                            $errors[] = "第 {$rowCount} 行 ({$prefix})：無法生成XML內容";
                        }
                    }
                }
            }
            fclose($handle);

            // 更新批次狀態
            $totalRows = $rowCount - 1;
            $finalStatus = ($failedCount == 0) ? 'completed' : (($successCount > 0) ? 'completed' : 'failed');
            $updateBatchSql = "UPDATE convert_batch SET total_rows = :totalRows, success_count = :successCount, failed_count = :failedCount, batch_status = :status WHERE batch_id = :batchId";
            $updateParams = [
                ':totalRows' => $totalRows,
                ':successCount' => $successCount,
                ':failedCount' => $failedCount,
                ':status' => $finalStatus,
                ':batchId' => $batchId
            ];
            $oDB->execute($updateBatchSql, $updateParams);

            $result['success'] = true;
            $result['message'] = "轉換完成！成功：{$successCount} 個XML檔案，失敗：{$failedCount} 個";

            if (!empty($errors)) {
                $result['message'] .= "\n錯誤詳情：\n" . implode("\n", array_slice($errors, 0, 5));
            }

        } else {
            throw new Exception('檔案讀取失敗');
        }

    } catch (Exception $e) {
        // 更新批次狀態為失敗
        $updateBatchSql = "UPDATE convert_batch SET batch_status = 'failed' WHERE batch_id = :batchId";
        $oDB->execute($updateBatchSql, [':batchId' => $batchId]);

        $result['message'] = '轉換失敗：' . $e->getMessage();
    }

    return $result;
}

/**
 * 從CSV行資料生成XML內容
 * @param array $headers CSV標題行（作為XML標籤）
 * @param array $data CSV資料行
 * @param string $userId 使用者ID
 * @param string $batchId 批次ID
 * @param string $prefix 檔案前綴 (SG08, SG18, SG28)
 * @param array $inspectionSettings 檢驗項目設定
 * @return string XML內容
 */
function generateXMLFromCSVRow($headers, $data, $userId = 'admin', $batchId = '', $prefix = '', $inspectionSettings = []) {
    try {
        // 建立XML文檔
        $xml = new DOMDocument('1.0', 'UTF-8');
        $xml->formatOutput = true;

        // 建立根元素
        $root = $xml->createElement('Micron_COA');
        $xml->appendChild($root);

        // 建立Header元素
        $header = $xml->createElement('Header');
        $root->appendChild($header);

        // 建立Content元素
        $content = $xml->createElement('Content');
        $root->appendChild($content);

        // 找到Cylinder Number作為UnitId
        $cylinderNumber = '';
        for ($i = 0; $i < count($headers); $i++) {
            $headerName = strtolower(trim($headers[$i]));
            if (strpos($headerName, 'cylinder number') !== false ||
                strpos($headerName, 'cylindernumber') !== false ||
                strpos($headerName, 'cylinder_number') !== false) {
                $cylinderNumber = isset($data[$i]) ? trim($data[$i]) : '';
                break;
            }
        }

        // 建立UnitId元素
        $unitId = $xml->createElement('UnitId');
        $unitId->setAttribute('Value', $cylinderNumber);
        $content->appendChild($unitId);

        // 收集檢驗項目名稱，用於判斷哪些欄位要放到Header
        $inspectionItemNames = [];
        if (!empty($inspectionSettings)) {
            foreach ($inspectionSettings as $setting) {
                $inspectionItemNames[] = strtolower($setting['itemName']);
            }
        }

        // 處理Header - 放置除了檢驗項目以外的所有欄位
        for ($i = 0; $i < count($headers) && $i < count($data); $i++) {
            $headerName = trim($headers[$i]);
            $headerNameLower = strtolower($headerName);
            $value = trim($data[$i]);

            // 跳過檢驗項目欄位
            if (!in_array($headerNameLower, $inspectionItemNames)) {
                $fieldElement = $xml->createElement('BasicInfoField');
                $fieldElement->setAttribute('FieldName', $headerName);
                $fieldElement->setAttribute('FieldValue', htmlspecialchars($value));
                $header->appendChild($fieldElement);
            }
        }

        // 處理Content - 放置檢驗項目
        if (!empty($inspectionSettings)) {
            foreach ($inspectionSettings as $setting) {
                $itemName = $setting['itemName'];
                $specification = $setting['specification'];
                $detectionLimit = $setting['detectionLimit'];

                // 在CSV中找到對應的值
                $inspectionValue = '';
                for ($i = 0; $i < count($headers); $i++) {
                    if (strtolower(trim($headers[$i])) === strtolower($itemName)) {
                        $inspectionValue = trim($data[$i]);
                        break;
                    }
                }

                // 如果找到值，生成InspectionItem元素
                if ($inspectionValue !== '') {
                    $inspectionItem = $xml->createElement('InspectionItem');
                    $inspectionItem->setAttribute('ItemName', $itemName);

                    // Unit (PURITY使用vol%，其他使用volppm)
                    $unitElement = $xml->createElement('ResultItem');
                    $unitElement->setAttribute('ResultName', 'Unit');
                    $unitValue = (strtoupper($itemName) === 'PURITY') ? 'vol%' : 'volppm';
                    $unitElement->setAttribute('Value', $unitValue);
                    $inspectionItem->appendChild($unitElement);

                    // Specification
                    $specElement = $xml->createElement('ResultItem');
                    $specElement->setAttribute('ResultName', 'Specification');
                    $specElement->setAttribute('Value', $specification);
                    $inspectionItem->appendChild($specElement);

                    // DetectionLimit
                    $limitElement = $xml->createElement('ResultItem');
                    $limitElement->setAttribute('ResultName', 'DetectionLimit');
                    $limitElement->setAttribute('Value', $detectionLimit);
                    $inspectionItem->appendChild($limitElement);

                    // InspectionValue (CSV中的實際值)
                    $valueElement = $xml->createElement('ResultItem');
                    $valueElement->setAttribute('ResultName', 'InspectionValue');
                    $valueElement->setAttribute('Value', $inspectionValue);
                    $inspectionItem->appendChild($valueElement);

                    $unitId->appendChild($inspectionItem);
                }
            }
        }

        return $xml->saveXML();

    } catch (Exception $e) {
        return false;
    }
}

/**
 * 清理XML標籤名稱，確保符合XML規範
 * @param string $tagName 原始標籤名稱
 * @return string 清理後的標籤名稱
 */
function sanitizeXMLTagName($tagName) {
    // 移除空白和特殊字符
    $tagName = trim($tagName);
    $tagName = preg_replace('/[^a-zA-Z0-9_\-]/', '_', $tagName);

    // 確保以字母開頭
    if (!preg_match('/^[a-zA-Z]/', $tagName)) {
        $tagName = 'Field_' . $tagName;
    }

    // 避免空標籤名稱
    if (empty($tagName)) {
        $tagName = 'UnknownField';
    }

    return $tagName;
}

/**
 * 驗證檢驗項目數值
 * @param array $headers CSV標題行
 * @param array $data CSV資料行
 * @param array $inspectionSettings 檢驗項目設定
 * @return array 驗證錯誤訊息
 */
function validateInspectionItems($headers, $data, $inspectionSettings) {
    $errors = [];

    foreach ($inspectionSettings as $setting) {
        $itemName = $setting['itemName'];
        $ucl = $setting['ucl'];

        // 在CSV標題中找到對應的欄位
        $columnIndex = -1;
        for ($i = 0; $i < count($headers); $i++) {
            if (strtolower(trim($headers[$i])) === strtolower($itemName)) {
                $columnIndex = $i;
                break;
            }
        }

        if ($columnIndex >= 0 && isset($data[$columnIndex])) {
            $value = floatval(trim($data[$columnIndex]));

            // 檢查是否超過UCL
            if ($ucl > 0 && $value > $ucl) {
                $errors[] = "檢驗項目 '{$itemName}' 的值 {$value} 超過上限值 {$ucl}";
            }
        }
    }

    return $errors;
}
?>
