<?php
// ini_set('display_errors','on') ;
ini_set('display_errors','off') ;
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/admin/Account.php");

$account_id = substr(trim($_REQUEST["id"]), 0, 10);
$passwd = substr(trim($_REQUEST["passwd"]), 0, 10);
if (substr(trim($_POST["login"]), 0, 1)=="1") {
  $oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
  if (!$oDB->open()) {
    die(CONNECTION_DB_FAILED.$oDB->error());
  }
  $oAccount = new Account($oDB);
  $login = $oAccount->isAuthorized($account_id, $passwd);
  if ($login == "S") {
    session_start();
    // session_register("ss_account");
    $_SESSION['ss_account'] = serialize($oAccount);
    // 產品沒有要開放後台
    // $url = DOMAIN_BACKEND_URL."PD/PRodList.php";
    $url = DOMAIN_BACKEND_URL."NEWS/NewsList.php";
    header("Location:".$url);
    exit(0);
  } else {
    if ($login == "F") {	//登入帳號或密碼錯誤
      $logon_count = $oAccount->getLogonCount();
      if ($logon_count == "" || $logon_count == null) {
        $logon_count = "999";
      } else {
        $logon_count = "000";
      }
    } elseif ($login == "T") {  //登入時間非在指定有效時間內
      $logon_count = "OUT OF TIME";
    } elseif ($login == "I") {	//登入IP不是在限定IP內
      $logon_count = "ILLEGAL IP";
    }
    header("Location:".NO_PRIV_PAGE."?action=$logon_count");
  }
}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>台灣力森諾科特殊氣體股份有限公司-管理平台</title>
<style type="text/css">
<!--
body {
	margin-left: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	background-image: url(images/bg_05.gif);
	background-repeat: repeat-x;
}
-->
</style>
<script type="text/javascript">
<!--
function MM_swapImgRestore() { //v3.0
  var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;
}
function MM_preloadImages() { //v3.0
  var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();
    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)
    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}
}

function MM_findObj(n, d) { //v4.01
  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
  if(!x && d.getElementById) x=d.getElementById(n); return x;
}

function MM_swapImage() { //v3.0
  var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)
   if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}
}
//-->
</script>
<script>
function chkValue() {
	f = window.document.theForm ;
	if ("" == f.id.value) {f.id.focus() ; return alert("請輸入「帳號」") ;}
	if ("" == f.passwd.value) {f.passwd.focus() ; return alert("請輸入「密碼」") ;}
	f.login.value=1;
	f.submit() ;
}

function resetValue() {
	f = window.document.theForm ;
	f.id.value = "";
	f.passwd.value = "";
}
</script>
</head>

<body onload="MM_preloadImages('images/into_bt_02.gif','images/into_bt_04.gif')">
<table width="600" border="0" align="center" cellpadding="0" cellspacing="0" id="___01">
  <tr>
    <td height="181" colspan="3">&nbsp;</td>
  </tr>
  <tr>
    <td colspan="3"><img src="images/into_02.gif" width="600" height="85" alt="" /></td>
  </tr>
  <tr>
    <td><img src="images/into_03.gif" width="234" height="74" alt="" /></td>
    <td width="211" height="74" bgcolor="#FFFFFF"><table width="211" border="0" cellspacing="0" cellpadding="0">
	<form method="post" name="theForm" action="into.php"><input type="hidden" name="login" value="">
      <tr>
        <th width="126" height="37" align="left" scope="row">          <label>
            <input name="id" type="text" id="id" size="15" maxlength="10"/>
          </label>        </th>
        <td width="85" height="37" align="center"><a href="javascript:chkValue()" onmouseout="MM_swapImgRestore()" onmouseover="MM_swapImage('Image5','','images/into_bt_02.gif',1)"><img src="images/into_bt_01.gif" name="Image5" width="65" height="23" border="0" id="Image5" /></a></td>
      </tr>
      <tr>
        <th height="37" align="left" scope="row"><input name="passwd" type="password" id="passwd" size="15" maxlength="10"/></th>
        <td height="37" align="center"><a href="javascript:resetValue()" onmouseout="MM_swapImgRestore()" onmouseover="MM_swapImage('Image6','','images/into_bt_04.gif',1)"><img src="images/into_bt_03.gif" name="Image6" width="65" height="23" border="0" id="Image6" /></a></td>
      </tr>
    </form></table></td>
    <td><img src="images/into_05.gif" width="155" height="74" alt="" /></td>
  </tr>
  <tr>
    <td colspan="3"><img src="images/into_06.gif" width="600" height="55" alt="" /></td>
  </tr>
</table>
</body>
</html>