<?php
// ini_set('display_errors','on') ;
ini_set('display_errors','off') ;
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/admin/Account.php");

$token = trim($_GET['token']);
$error_msg = "";
$success_msg = "";
$account_id = "";
$token_valid = false;

// 驗證 token
if ($token) {
    session_start();
    $token_key = 'reset_token_' . $token;
    
    if (isset($_SESSION[$token_key])) {
        $token_data = $_SESSION[$token_key];
        
        // 檢查是否過期
        if (time() <= $token_data['expire_time']) {
            $account_id = $token_data['account_id'];
            $token_valid = true;
        } else {
            $error_msg = "重設連結已過期，請重新申請。";
            unset($_SESSION[$token_key]);
        }
    } else {
        $error_msg = "無效的重設連結。";
    }
} else {
    $error_msg = "缺少重設參數。";
}

// 處理密碼重設
if ($_POST['action'] == 'reset_password' && $token_valid) {
    $new_password = trim($_POST['new_password']);
    $confirm_password = trim($_POST['confirm_password']);
    
    if (empty($new_password)) {
        $error_msg = "請輸入新密碼。";
    } elseif (strlen($new_password) < 6) {
        $error_msg = "密碼長度至少需要6個字元。";
    } elseif ($new_password !== $confirm_password) {
        $error_msg = "兩次輸入的密碼不一致。";
    } else {
        $oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
        if (!$oDB->open()) {
            die(CONNECTION_DB_FAILED.$oDB->error());
        }
        
        $oAccount = new Account($oDB);
        if ($oAccount->getFromDb($account_id)) {
            $result = $oAccount->updateSPwd($new_password);
            
            if ($result == "") {
                $success_msg = "密碼重設成功！請使用新密碼登入。";
                // 清除 token
                unset($_SESSION[$token_key]);
                $token_valid = false;
            } else {
                $error_msg = "密碼重設失敗：" . $result;
            }
        } else {
            $error_msg = "帳號不存在。";
        }
    }
}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>密碼重設 - 後台管理系統</title>
<style type="text/css">
body {
    font-family: Arial, sans-serif;
    background-color: #f5f5f5;
    margin: 0;
    padding: 20px;
}
.container {
    max-width: 400px;
    margin: 50px auto;
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}
.title {
    text-align: center;
    color: #0082DA;
    margin-bottom: 30px;
    font-size: 24px;
}
.form-group {
    margin-bottom: 20px;
}
.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #333;
    font-weight: bold;
}
.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    box-sizing: border-box;
}
.btn {
    width: 100%;
    padding: 12px;
    background-color: #0082DA;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    margin-top: 10px;
}
.btn:hover {
    background-color: #006bb3;
}
.error {
    color: #ff0000;
    background-color: #ffe6e6;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 20px;
    text-align: center;
}
.success {
    color: #008000;
    background-color: #e6ffe6;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 20px;
    text-align: center;
}
.back-link {
    text-align: center;
    margin-top: 20px;
}
.back-link a {
    color: #0082DA;
    text-decoration: none;
}
.back-link a:hover {
    text-decoration: underline;
}
</style>
<script>
function validateForm() {
    var newPassword = document.getElementById('new_password').value;
    var confirmPassword = document.getElementById('confirm_password').value;
    
    if (newPassword == '') {
        alert('請輸入新密碼');
        return false;
    }
    
    if (newPassword.length < 6) {
        alert('密碼長度至少需要6個字元');
        return false;
    }
    
    if (newPassword != confirmPassword) {
        alert('兩次輸入的密碼不一致');
        return false;
    }
    
    return true;
}
</script>
</head>

<body>
<div class="container">
    <h1 class="title">密碼重設</h1>
    
    <?php if ($error_msg): ?>
        <div class="error"><?php echo htmlspecialchars($error_msg); ?></div>
    <?php endif; ?>
    
    <?php if ($success_msg): ?>
        <div class="success"><?php echo htmlspecialchars($success_msg); ?></div>
    <?php endif; ?>
    
    <?php if ($token_valid): ?>
        <p style="text-align: center; margin-bottom: 20px;">
            <strong>帳號：</strong><?php echo htmlspecialchars($account_id); ?>
        </p>
        
        <form method="post" action="reset_password.php?token=<?php echo htmlspecialchars($token); ?>" onsubmit="return validateForm()">
            <input type="hidden" name="action" value="reset_password">
            
            <div class="form-group">
                <label for="new_password">新密碼：</label>
                <input type="password" id="new_password" name="new_password" maxlength="20" />
            </div>
            
            <div class="form-group">
                <label for="confirm_password">確認新密碼：</label>
                <input type="password" id="confirm_password" name="confirm_password" maxlength="20" />
            </div>
            
            <button type="submit" class="btn">重設密碼</button>
        </form>
    <?php endif; ?>
    
    <div class="back-link">
        <a href="into.php">返回登入頁面</a>
    </div>
</div>
</body>
</html>
