<?php

require_once(LIBRARY_DIR."library/classes/utils/Tools.php");
require_once(LIBRARY_DIR."library/classes/utils/ErrorHandler.php");
require_once(LIBRARY_DIR."library/classes/database/DB4PDO.php");
require_once(LIBRARY_DIR."library/classes/acceptsObject.php");

/******************* Class Account *********************/
class Account extends DBObject{
    public $account_id;	//帳號
    public $name;		//姓名
    public $passwd;		//密碼
    public $teleno;		//連絡電話
    public $email;		//電子郵件
    public $address;		//連絡地址
    public $status;		//狀態  0:停用	1:啟用
    public $priv;		//權限  a:最大權限
    public $cdate;		//建檔日期
    public $mdate;		//修改日期
    public $last_in_time;	//最近登入時間
    public $logon_count;	//登入次數
    public $s_valid_time;	//可登入起始時間
    public $e_valid_time;	//可登入最後時間
    public $ip_list;		//限定登入IP,空白表示任何此帳號可用任何IP登入

    public $status_name;
    public $priv_name;

    function __construct($db){
        // $this->DBObject();
        parent::__construct();
        $this->setDb($db);
    }
    function getFromDb($pk){
        $founded = false;
        $sql = "SELECT * FROM ACCOUNT WHERE ACCOUNT_ID = ? ";
        $param = array($pk);
		$this->db->execute($sql, $param);
        while ($this->db->fetchRow()) {
            $this->setAccount($this->db->rowArray);
            $founded = true;
        }
        return $founded;
    }
    function isAuthorized($id, $pwd){
        $h = "F";
        $id = trim($id);
        $pwd = md5(trim($pwd));
        if (DOOR == "OPEN" || $id == "admin") {
            if ($this->getFromDb($id)) {
                $login_time = date("H:m:s");
                $s_time = $this->s_valid_time;
                $e_time = $this->e_valid_time;
                $ip = explode(",", $this->ip_list);
                if ($login_time >= $s_time && $login_time <= $e_time) {//判斷是否在有效時間內登入
                    if (in_array(REMOTE_IP, $ip) || $this->ip_list == "") {	//判斷是否是在限定的IP內登入,
                        if ($this->passwd==$pwd && $this->status=="1" && $this->logon_count <= LOGON_TIMES) {
                            $this->updateLoginTime("S");
                            $h="S";
                        } else {
                            $this->updateLoginTime("F");
                            $h="F";
                        }
                    } else {
                        $this->updateLoginTime("I");
                        $h="I";
                    }
                } else {
                    $this->updateLoginTime("T");
                    $h="T";
                }
            }
        }
        return $h;
    }
    function setAccount($pData){
        foreach($pData as $k=>$v){
			$k = strtolower($k);
			$this->$k = $v;
		}
    }
    function updateLoginTime($action){
        $paramAry = array();
        $loginTime = date("Y/m/d H:i:s");
        $this->last_in_time = $loginTime;
        $this->logon_count = $this->logon_count+1;
        if ($action == "S") { //登入OK
            $sql = "UPDATE ACCOUNT SET LAST_IN_TIME = :loginTime,LOGON_COUNT = 0 WHERE ACCOUNT_ID = :pk";
        } else {		//登入Fail
            $sql = "UPDATE ACCOUNT SET LAST_IN_TIME = :loginTime,LOGON_COUNT = LOGON_COUNT + 1 WHERE ACCOUNT_ID = :pk";
        }
        $paramAry[':loginTime'] = $loginTime;
        $paramAry[':pk'] = $this->account_id;
        return $this->db->execute($sql, $paramAry);
    }
    function setAddAccount(){
        $n = func_num_args();
        $this->account_id = trim(func_get_arg(0));
        $this->name       = trim(func_get_arg(1));
        $this->passwd     = trim(md5(func_get_arg(2)));
        $this->teleno 	  = trim(func_get_arg(3));
        $this->email 	  = trim(func_get_arg(4));
        $this->address    = trim(func_get_arg(5));
        $this->status	  = trim(func_get_arg(6));
        $this->priv       = trim(func_get_arg(7));
        $this->s_valid_time = trim(func_get_arg(8));
        $this->e_valid_time = trim(func_get_arg(9));
        $this->ip_list 	= trim(func_get_arg(10));
        $this->cdate      = date("Y-m-d");
    }
    function addRule(){
        $status = 0;
        if ($this->hasAccountID($this->account_id)) {
            $status = 1;
        } elseif ($this->account_id == "") {
            $status = 2;
        } elseif ($this->name == "") {
            $status = 3;
        } elseif ($this->passwd == "") {
            $status = 4;
        }
        return $status;
    }
    function add(){
        $status = $this->addRule();
        $msg = "";
        switch ($status) {
            case 0:
                $sql = "INSERT INTO ACCOUNT(ACCOUNT_ID,NAME,PASSWD,TELENO,EMAIL,ADDRESS,STATUS,PRIV,CDATE,S_VALID_TIME,
                        E_VALID_TIME,IP_LIST) VALUES( :account_id, :name, :passwd, :teleno, :email, :address,
                        :status, :priv, :cdate , :s_valid_time, :e_valid_time, :ip_list)";
                $paramAry[':account_id'] = $this->account_id;
                $paramAry[':name'] = $this->name;
                $paramAry[':passwd'] = $this->passwd;
                $paramAry[':teleno'] = $this->teleno;
                $paramAry[':email'] = $this->email;
                $paramAry[':address'] = $this->address;
                $paramAry[':status'] = $this->status;
                $paramAry[':priv'] = $this->priv;
                $paramAry[':cdate'] = $this->cdate;
				$paramAry[':s_valid_time'] = $this->s_valid_time;
				$paramAry[':e_valid_time'] = $this->e_valid_time;
				$paramAry[':ip_list'] = $this->ip_list;
				$paramAry[':h_id'] = $this->h_id;
				$rs = $this->db->execute($sql, $paramAry);
                if ($rs < 1) {
                    $msg="新增帳號資料失敗";
                }
                break;
            case 1:
                $msg = "帳號重覆！！";
                break;
            case 2:
                $msg = "帳號不能空白！！";
                break;
            case 3:
                $msg = "名稱不能空白！！";
                break;
            case 4:
                $msg = "密碼不能空白！！";
                break;
        }
        return $msg;
    }
    function setUpdateAccount(){
        $n = func_num_args();
        $this->account_id = trim(func_get_arg(0));
        $this->name   = trim(func_get_arg(1));
        $this->teleno = trim(func_get_arg(2));
        $this->email = trim(func_get_arg(3));
        $this->address = trim(func_get_arg(4));
        $this->status = trim(func_get_arg(5));
        $this->priv   = trim(func_get_arg(6));
        $this->logon_count   = trim(func_get_arg(7));
        $this->s_valid_time = trim(func_get_arg(8));
        $this->e_valid_time = trim(func_get_arg(9));
        $this->ip_list = trim(func_get_arg(10));
        $this->mdate  = date("Y-m-d");
    }
    function updateRule(){
        $status = 0;
        if ($this->name == "") {
            $status = 1;
        }
        return $status;
    }
    function update(){
        $status = $this->updateRule();
        $msg = "";
        switch ($status) {
            case 0:
                $sql = "UPDATE ACCOUNT SET NAME = :name ,TELENO = :teleno ,EMAIL = :email ,ADDRESS = :address,
                STATUS = :status ,PRIV = :priv,LOGON_COUNT = :logon_count,MDATE = :mdate,S_VALID_TIME = :s_valid_time,
                E_VALID_TIME = :e_valid_time,IP_LIST = :ip_list WHERE ACCOUNT_ID = :pk "; 
                $paramAry[':name'] = $this->name;
                $paramAry[':teleno'] = $this->teleno;
                $paramAry[':email'] = $this->email;
                $paramAry[':address'] = $this->address;
                $paramAry[':status'] = $this->status;
                $paramAry[':priv'] = $this->priv;
                $paramAry[':logon_count'] = $this->logon_count;
                $paramAry[':mdate'] = $this->mdate;
                $paramAry[':s_valid_time'] = $this->s_valid_time;
                $paramAry[':e_valid_time'] = $this->e_valid_time;
                $paramAry[':ip_list'] = $this->ip_list;
                $paramAry[':pk'] = $this->account_id;
                $rs = $this->db->execute($sql, $paramAry);
                if ($rs < 1) {
                    $msg="修改帳號資料失敗!!";
                }
                break;
            case 1:
                $msg = "名稱不得空白！！";
                break;
        }
        return $msg;
    }
    function updatePwd($oldpwd, $newpwd){
        $msg = "";
        $sql = "SELECT ACCOUNT_ID FROM ACCOUNT WHERE ACCOUNT_ID = :pk AND PASSWD = :pwd ";
        $paramAry = array();
        $paramAry[':pwd'] = md5(trim($oldpwd));
        $paramAry[':pk'] = $this->account_id;
		$this->db->execute($sql, $param);
        // if ($this->db->queryRow > 0) {
        if ($this->db->fetchRow()){
            $sql = "UPDATE ACCOUNT SET PASSWD = :pwd WHERE ACCOUNT_ID = :pk ";
            $paramAry = array();
            $paramAry[':pwd'] = md5(trim($newpwd));
            $paramAry[':pk'] = $this->account_id;
            $rs = $this->db->execute($sql, $paramAry);
            if ($rs < 1) {
                $msg = "密碼更新失敗!!";
            }
        } else {
            $msg="舊密碼不對!! ";
        }
        return $msg;
    }
    function updateSPwd($newpwd){
        $msg = "";
        $sql = "UPDATE ACCOUNT SET PASSWD = :pwd WHERE ACCOUNT_ID = :pk ";
        $paramAry = array();
        $paramAry[':pwd'] = md5(trim($newpwd));
        $paramAry[':pk'] = $this->account_id;
        $rs = $this->db->execute($sql, $paramAry);
        if ($rs < 1) {
            $msg = "密碼更新失敗!!";
        }
        return $msg;
    }
    function del(){
        $status = $this->delRule();
        $msg = "";
        switch ($status) {
            case 0:
                $sql = "DELETE FROM ACCOUNT WHERE ACCOUNT_ID = :pk ";
                $paramAry = array();
                $paramAry[':pk'] = $this->account_id;
                $rs = $this->db->execute($sql, $paramAry);
                if ($rs < 1) {
                    $msg = "刪除資料失敗!!";
                }
                break;
            case 1:
                $msg = "預設管理者帳號不能刪除！！";
                break;
        }
        return $msg;
    }
    function delRule(){
        $status = 0;
        if ($this->account_id == "admin") {
            $status = 1;
        }
        return $status;
    }
    function hasAccountID($oID){
        $sql = "SELECT ACCOUNT_ID FROM ACCOUNT WHERE ACCOUNT_ID = ? ";
        $param = array($oID);
		$this->db->execute($sql, $param);
        if ($this->db->fetchRow()) {
            return true;
        } else {
            return false;
        }
    }
    function getStatusName(){
        $oStatus = new AccountStatus();
        $oStatus->getFromDb($this->status);
        return $oStatus->getName();
    }
    function getPrivName(){
        $oPriv = new Priv();
        $oPriv->getFromDb($this->priv);
        return $oPriv->getName();
    }
    function setAccountID($s){
        $this->account_id=$s;
    }
    function setName($s){
        $this->name=$s;
    }
    function setPasswd($s){
        $this->passwd=$s;
    }
    function setTeleNo($s){
        $this->teleno = $s;
    }
    function setEMail($s){
        $this->email = $s;
    }
    function setAddress($s){
        $this->address = $s;
    }
    function setStatus($s){
        $this->status = $s;
        $this->setStatusName();
    }
    function setPriv($s){
        $this->priv=$s;
        $this->setPrivName();
    }
    function setCDate($s){
        $this->cdate = $s;
    }
    function setMDate($s){
        $this->mdate = $s;
    }
    function setLastInTime($s){
        $this->last_in_time=$s;
    }
    function setLogonCount($s){
        $this->logon_count=$s;
    }
    function setFunctions($s){
        $this->functions=$s;
    }
    function setSValidTime($s){
        $this->s_valid_time=$s;
    }
    function setEValidTime($s){
        $this->e_valid_time=$s;
    }
    function setIPList($s){
        $this->ip_list=$s;
    }
    function getAccountID(){
        return $this->account_id;
    }
    function getName(){
        return $this->name;
    }
    function getPasswd(){
        return $this->passwd;
    }
    function getTeleNo(){
        return $this->teleno;
    }
    function getEMail(){
        return $this->email;
    }
    function getAddress(){
        return $this->address;
    }
    function getStatus(){
        return $this->status;
    }
    function getPriv(){
        return $this->priv;
    }
    function getLastInTime(){
        return $this->last_in_time;
    }
    function getCDate(){
        return $this->cdate;
    }
    function getMDate(){
        return $this->mdate;
    }
    function getLogonCount(){
        return $this->logon_count;
    }
    function getFunctions(){
        return $this->functions;
    }
    function getSValidTime(){
        return $this->s_valid_time;
    }
    function getEValidTime(){
        return $this->e_valid_time;
    }
    function getIPList(){
        return $this->ip_list;
    }
}
/******************* End Class Account *****************/

/******************* Class AccountList *****************/
class AccountList extends PageVector{
    public $db = null;
    function __construct($db){
        // $this->PageVector();
        parent::__construct();
        $this->setDb($db);
    }
    function setDb($db){
        $this->db = $db;
    }
    function getAccountList($status){
        $param = array();
        $sql = "SELECT * FROM ACCOUNT WHERE 1 ";
        if ($status != "") {
            $sql .= " AND STATUS = :status ";
            $param[':status'] = $status;
        }
        $sql .= " ORDER BY ACCOUNT_ID";
        $this->db->execute($sql, $param);
        while ($this->db->fetchRow()) {
            $g = new Account($this->db);
            $g->setAccount($this->db->rowArray);
            $this->add($g);
        }
    }
}
/******************* End Class AccountList *************/
